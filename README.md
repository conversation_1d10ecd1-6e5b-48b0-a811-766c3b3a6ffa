# 对抗环境-智能体系统

基于PyTorch实现的对抗训练深度神经网络系统，包含可学习的环境网络E和智能体网络A。

## 系统特点

- **对抗训练机制**: 环境E试图最大化智能体A的损失，智能体A试图最小化自己的任务损失
- **部分信息交换**: 环境和智能体通过部分位置交换信息，模拟现实中的部分可观测性
- **物理约束**: 环境网络包含物理合理性约束，防止生成不合理的动态
- **多样性机制**: 鼓励环境生成多样化的状态，避免模式崩塌
- **渐进式训练**: 预热阶段固定环境，逐步释放对抗训练

## 文件结构

```
├── config.py              # 配置文件，定义超参数
├── networks.py            # 神经网络定义（环境网络E和智能体网络A）
├── adversarial_trainer.py # 对抗训练器核心逻辑
├── evaluation_tasks.py    # 评估任务（趋近点和稀疏奖励）
├── main.py               # 主训练脚本
├── demo.py               # 快速演示脚本
├── requirements.txt      # 依赖包列表
└── README.md            # 说明文档
```

## 安装依赖

```bash
pip install -r requirements.txt
```

## 快速开始

### 1. 运行演示

```bash
python demo.py
```

这将运行一个快速演示，展示对抗训练的基本过程和结果。

### 2. 完整训练

```bash
python main.py
```

可选参数：
- `--save_dir`: 指定结果保存目录（默认: ./results）
- `--resume`: 从检查点恢复训练
- `--eval_only`: 仅运行评估模式

### 3. 从检查点恢复训练

```bash
python main.py --resume ./results/experiment_20231201_120000/checkpoint_episode_5000.pth
```

## 系统架构

### 数学框架

**环境网络E**:
```
c_{t+1}, a_{t+1} = E(c_t, a_t, x_t[sub_x])
```

**智能体网络A**:
```
x_{t+1}, y_{t+1} = A(x_t, y_t, c_t[sub_c])
```

**变量定义**:
- `x`: 智能体与环境直接交互的量（真正的输出量）
- `y`: 智能体的内部状态
- `c`: 环境上下文
- `a`: 环境动作

**损失函数**:
```
Loss_A = ||y_{sub_loss}||^2 + task_specific_loss + consistency_term
Loss_E = -α * Loss_A + β * physics_constraint + γ * diversity_term
```

### 网络结构

- **环境网络E**: 编码器 + 上下文预测头 + 动作预测头 + 物理约束层
- **智能体网络A**: 编码器 + 状态预测头 + 输出预测头

### 训练策略

1. **预热阶段** (0-2000轮): 环境固定，智能体学习基础技能
2. **对抗阶段** (2000轮后): 环境和智能体交替对抗训练
3. **平衡机制**: 动态调整学习率，防止训练不稳定

## 评估任务

### 1. 趋近点任务
测试智能体是否能够趋近指定目标点，包含三个难度级别：
- Easy: 目标点在原点附近
- Medium: 目标点在中等距离
- Hard: 目标点距离较远

### 2. 稀疏奖励任务
测试智能体在稀疏奖励环境中的学习能力，只有当满足特定条件时才给予奖励。

## 配置参数

主要配置参数在 `config.py` 中：

```python
# 网络架构
state_dim = 64          # 状态维度
action_dim = 32         # 动作维度
context_dim = 128       # 上下文维度

# 信息交换
sub_x_ratio = 0.3       # 智能体向环境传递的信息比例
sub_c_ratio = 0.4       # 环境向智能体传递的信息比例

# 对抗训练平衡
alpha = 1.0             # 环境损失中智能体损失的权重
beta = 0.1              # 物理约束权重
gamma = 0.05            # 多样性项权重
```

## 结果分析

训练完成后，系统会生成：

1. **训练曲线**: 智能体和环境的损失变化
2. **评估结果**: 两类任务的性能指标
3. **检查点**: 可用于恢复训练的模型状态
4. **训练指标**: 详细的训练过程数据

## 实验建议

1. **基线对比**: 先运行固定环境版本作为基线
2. **超参数调优**: 调整α、β、γ等平衡参数
3. **网络架构**: 尝试不同的网络深度和宽度
4. **信息交换比例**: 实验不同的sub_x_ratio和sub_c_ratio

## 注意事项

- 对抗训练可能不稳定，建议从较小的学习率开始
- 物理约束和多样性权重需要仔细调节
- 建议使用GPU加速训练
- 定期保存检查点以防训练中断

## 扩展方向

1. **更复杂的环境动态**: 添加更多物理约束
2. **多智能体系统**: 扩展到多个智能体的对抗训练
3. **连续控制任务**: 适配到连续控制问题
4. **元学习**: 让智能体学会快速适应新环境
