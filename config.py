"""
配置文件 - 定义系统的超参数和架构参数
"""

import torch

class Config:
    # 设备配置
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 网络架构参数
    state_dim = 64          # 状态维度
    action_dim = 32         # 动作维度
    context_dim = 128       # 上下文维度
    hidden_dim = 256        # 隐藏层维度
    
    # 信息交换参数
    sub_x_ratio = 0.3       # 智能体向环境传递的信息比例
    sub_c_ratio = 0.4       # 环境向智能体传递的信息比例
    
    # 训练参数
    batch_size = 64
    learning_rate_A = 1e-4  # 智能体学习率
    learning_rate_E = 5e-5  # 环境学习率（较小，避免过快变化）
    
    # 对抗训练平衡参数
    alpha = 1.0             # 环境损失中智能体损失的权重
    beta = 0.1              # 物理约束权重
    gamma = 0.05            # 多样性项权重
    
    # 训练策略参数
    warmup_episodes = 1000  # 预热阶段，环境固定
    total_episodes = 10000  # 总训练轮数
    adversarial_start = 2000 # 开始对抗训练的轮数
    
    # 评估参数
    eval_episodes = 100     # 评估轮数
    eval_frequency = 500    # 评估频率
    
    # 任务参数
    target_tolerance = 0.1  # 趋近点任务的容忍度
    sparse_reward_threshold = 0.05  # 稀疏奖励阈值
    
    # 正则化参数
    physics_constraint_weight = 1.0  # 物理约束权重
    diversity_weight = 0.1          # 多样性权重
    stability_weight = 0.05         # 稳定性权重
